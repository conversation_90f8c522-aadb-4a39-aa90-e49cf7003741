package net.summerfarm.mall.task;

import net.summerfarm.common.util.Prompt;
import net.summerfarm.common.util.PropertiesUtils;
import net.summerfarm.common.util.URLUtils;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.service.CategoryService;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.service.facade.AuthWechatFacade;
import net.summerfarm.mall.wechat.token.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Package: net.summerfarm.task
 * @Description: 事件监听器（在spring容器启动后）
 * @author: <EMAIL>
 * @Date: 2018/8/28
 */
@Component
public class EventAfterInit implements ApplicationListener<ContextRefreshedEvent> {

    private static final Logger logger = LoggerFactory.getLogger(EventAfterInit.class);

    @Resource
    private CategoryService categoryService;
    @Resource
    private ConfigService configService;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private TokenService tokenService;
    @Resource
    private AuthWechatFacade authWechatFacade;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if(event.getApplicationContext().getParent() != null){
            logger.info("防止重复执行");
            return;
        }
        logger.info("contextInitialized……");

        //域名
        Global.DOMAIN_NAME = PropertiesUtils.getProperty("xianmu.mall.domain");
        Global.POP_DOMAIN_NAME = PropertiesUtils.getProperty("pop.mall.domain");
        Global.TOP_DOMAIN_NAME = URLUtils.getDomainName(Global.DOMAIN_NAME);
        logger.info("--------------使用的域名为：{}", Global.DOMAIN_NAME);

        //conf更新
        Conf.flush();

        logger.info("加载配置的提示语给前端...");
        Prompt.processProperties();

        logger.info("加载类目树数据...");
        categoryService.flushCategoryTree();

        logger.info("加载运营服务区数据...");
        List<Area> areaList=areaMapper.select();
        for (Area area:areaList) {
            Global.areaMap.put(area.getAreaNo(),area.getAreaName());
        }

        logger.info("加载配置项到内存...");
        configService.select();

        logger.info("加载数据完成...");
    }
}
