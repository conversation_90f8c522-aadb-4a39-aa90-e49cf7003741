package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;


/**
 *  售后反查
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefundByAfterSaleOrderNoJob extends XianMuJavaProcessorV2 {

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("售后反查 start :{}", LocalDateTime.now());
//        paymentHandler.refundByAfterSaleOrderNo(context.getJobParameters());
//        log.info("售后反查 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}