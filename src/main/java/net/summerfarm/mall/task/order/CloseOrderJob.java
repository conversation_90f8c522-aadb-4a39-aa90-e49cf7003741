package net.summerfarm.mall.task.order;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.domain.Orders;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.OrderRelationService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @author: <EMAIL>
 * @create: 2023/12/18
 */
@Slf4j
@Component
public class CloseOrderJob extends XianMuJavaProcessorV2 {

    private static final String ORDER_NO_KEY = "orderNo";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        return new ProcessResult(true);
    }
}
