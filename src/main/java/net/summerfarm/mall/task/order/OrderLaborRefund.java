package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.service.RefundService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 *  订单人工退款
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderLaborRefund  extends XianMuJavaProcessorV2 {


    @Resource
    private RefundService refundService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("订单人工退款 start :{}", LocalDateTime.now());

        List<String> orderNoList = null;
        if (!StringUtils.isBlank(context.getInstanceParameters())){
            log.info("实例参数:{}", context.getInstanceParameters());
            orderNoList = Arrays.asList(context.getInstanceParameters().split(","));
        }

        if (CollectionUtils.isEmpty(orderNoList)){
            log.info("无订单人工退款订单 end :{}", LocalDateTime.now());
            return new ProcessResult(true);
        }

        for (String orderNo : orderNoList){
            try {
                refundService.laborRefund(orderNo);
            }catch (Exception e){
                log.error("省心送订单自动退款异常，orderNo:{}，msg:{}",orderNo,e.getMessage(),e);
            }
        }

        log.info("订单人工退款 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }

}
