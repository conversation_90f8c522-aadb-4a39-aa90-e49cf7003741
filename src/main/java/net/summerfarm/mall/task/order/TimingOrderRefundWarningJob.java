package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

/**
 * 即将退款提醒（T为自动退款日期，在T-1天、T-3天、T-7天、T-14天、T-30天触发短信和公众号提醒）
 * 和短信定时任务合并，这里不处理
 *
 * @author: <EMAIL>
 * @create: 2023/5/12
 */
@Slf4j
@Component
public class TimingOrderRefundWarningJob extends XianMuJavaProcessorV2 {

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("【省心送退款预警】通知【开始处理】");
        //afterSaleOrderService.timingRefundSms(2);
        log.info("【省心送退款预警】通知【结束处理】");
        return new ProcessResult(true);
    }
}
