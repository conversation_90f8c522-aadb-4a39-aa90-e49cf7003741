package net.summerfarm.mall.task.order;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 *  订单自动确认收货
 * <AUTHOR>
 */
@Component
@Slf4j
public class AutoConfirmJob extends XianMuJavaProcessorV2 {

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("订单自动确认收货 start :{}", LocalDateTime.now());
//        deliveryPlanService.autoConfirm();
        log.info("订单自动确认收货 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}