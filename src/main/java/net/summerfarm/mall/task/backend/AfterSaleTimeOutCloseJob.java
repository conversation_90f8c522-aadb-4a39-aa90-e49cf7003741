package net.summerfarm.mall.task.backend;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.delayqueue.AfterSaleProofItem;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 *  售后凭证超时自动关闭售后单定时任务后门
 * <AUTHOR>
 */
@Component
@Slf4j
public class AfterSaleTimeOutCloseJob extends XianMuJavaProcessorV2{

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("售后凭证超时自动关闭售后单定时任务后门 start :{}", LocalDateTime.now());

        List<String> afterSaleOrderNoList = null;
        if (!StringUtils.isBlank(context.getInstanceParameters())){
            log.info("实例参数:{}", context.getInstanceParameters());
            afterSaleOrderNoList = Arrays.asList(context.getInstanceParameters().split(","));
        }
        //为空为定时任务自动触发进行系统查询，否则为人工手动触发
        if (CollectionUtils.isEmpty(afterSaleOrderNoList)){
            log.info("参数为空查询数据库售后凭证超时");
            afterSaleOrderNoList =  afterSaleOrderService.getAfterSaleTimeOutNo();
        }

        if (CollectionUtils.isEmpty(afterSaleOrderNoList)){
            log.info("无售后凭证超时售后单 end :{}", LocalDateTime.now());
            return new ProcessResult(true);
        }
        for (String afterSaleOrderNo : afterSaleOrderNoList){
            try {
                AfterSaleProofItem afterSaleProofItem = new AfterSaleProofItem();
                afterSaleProofItem.setAfterSaleOrderNo(afterSaleOrderNo);
                afterSaleOrderService.closeProof(afterSaleProofItem);
            }catch (Exception e){
                log.error("售后凭证超时自动关闭售后单异常，afterSaleOrderNo:{}，msg:{}",afterSaleOrderNo,e.getMessage(),e);
            }
        }

        log.info("售后凭证超时自动关闭售后单定时任务后门 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }

}
