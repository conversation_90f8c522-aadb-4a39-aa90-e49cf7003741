package net.summerfarm.mall.task.afterSale;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 *  省心送数据刷新
 * <AUTHOR>
 */
@Component
@Slf4j
public class TimingDataRefreshJob extends XianMuJavaProcessorV2 {

    @Resource
    private AfterSaleOrderService afterSaleOrderService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("省心送数据刷新 start :{}", LocalDateTime.now());
//        afterSaleOrderService.timingDataRefresh();
        log.info("省心送数据刷新 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }

}
