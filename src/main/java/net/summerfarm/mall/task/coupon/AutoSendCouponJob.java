package net.summerfarm.mall.task.coupon;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 自动发放卡劵补偿机制
 * @date 2023/12/15 14:17:56
 */
@Component
@Slf4j
public class AutoSendCouponJob extends XianMuJavaProcessorV2 {

    @Resource
    private CouponSenderContext couponSenderContext;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("AutoSendCouponJob[]processResult[]start:{}", LocalDateTime.now());
//        couponSenderContext.autoSendCouponTask(context.getJobParameters());
//        log.info("AutoSendCouponJob[]processResult[]end:{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
