package net.summerfarm.mall.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.enums.SMSType;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.sms.SMSSenderFactory;
import net.summerfarm.mall.common.thread.TagThread;
import net.summerfarm.mall.common.util.MailUtil;
import net.summerfarm.mall.common.util.SplitUtils;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.SMS;
import net.summerfarm.mall.model.domain.AreaStore;
import net.summerfarm.mall.model.domain.Merchant;
import net.summerfarm.mall.model.domain.PurchasesConfig;
import net.summerfarm.mall.model.input.AdminMsgInput;
import net.summerfarm.mall.model.vo.BatchUpdateDeliveryDateVo;
import net.summerfarm.mall.model.vo.DeliveryPlanChangeWeChatVo;
import net.summerfarm.mall.service.ConfigService;
import net.summerfarm.mall.wechat.templatemessage.DeliveryPlanChangeMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Package: net.summerfarm.task
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/5/17
 */
@Component
@Async
@Lazy
public class AsyncTaskService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncTaskService.class);
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private ConfigService configService;
    @Resource
    private MailUtil mailUtil;

    private ScheduledExecutorService scheduledExecutorService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private SMSSenderFactory smsSenderFactory;
    @Resource
    private TemplateMsgSender templateMsgSender;

    public void adminMsg(AdminMsgInput adminMsgInput) {
        MQData mqData  = new MQData();
        mqData.setType(MType.ADMIN_MSG.name());
        mqData.setData(JSON.toJSONString(adminMsgInput));
        mqProducer.send("mall",null, JSON.toJSONString(mqData));
    }

    public void purchasesArrival(PurchasesConfig purchasesConfig){
        MQData mqData = new MQData();
        mqData.setType(MType.PURCHASES_CONFIG.name());
        mqData.setData(JSON.toJSONString(purchasesConfig));
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null, JSON.toJSONString(mqData));
    }

    /**
     * 省心送订单加冻结 (明日配送且未加冻结的订单)
     */
    public void timingOrderLock(String sku, Integer storeNo) {
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(sku);
        areaStore.setAreaNo(storeNo);
        MQData mqData = new MQData(MType.TIMING_ORDER_LOCK_TWO.name());
        mqData.setData(areaStore);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
    }


    public void dingtalkProcess(String sku, Integer storeNo){
        AreaStore areaStore = new AreaStore();
        areaStore.setSku(sku);
        areaStore.setAreaNo(storeNo);
        MQData mqData = new MQData();
        mqData.setData(JSON.toJSONString(areaStore));
        mqData.setType(MType.DING_TALK_PROCESS.name());
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));

    }

    public void dingTalkRobotBigOrderMsg(String orderNo,String sku,String dpId){
        MQData mqData = new MQData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderNo",orderNo);
        jsonObject.put("sku",sku);
        jsonObject.put("dpId",dpId);
        mqData.setType(MType.DING_TALK_BIG_ORDER_MSG.name());
        mqData.setData(jsonObject);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST,null, JSON.toJSONString(mqData));
    }

    /**
    * 更新标签信息
    */
    public void updateTag(List<Merchant> merchants, Integer tagId, String tagName){

        //校验线程池
        if (scheduledExecutorService == null || scheduledExecutorService.isShutdown()) {
            scheduledExecutorService = new ScheduledThreadPoolExecutor(5,
                    new BasicThreadFactory.Builder().namingPattern("tag-schedule-pool-%d").daemon(true).build());
        }
        //数据拆分
        //分组操作
        //全部执行
        //结束发送邮件
        CountDownLatch countDownLatch = new CountDownLatch(5);
        //分组执行
        List<List<Merchant>> merchantLists = SplitUtils.avgSplit(merchants, 5);
        for (List<Merchant> merchantList : merchantLists) {
            scheduledExecutorService.execute(new TagThread(merchantList,countDownLatch,tagId,tagName));
        }
        try {
            countDownLatch.await(15, TimeUnit.MINUTES);
            //消息通知完成
            String title = "客户打标完成";
            String content = "客户打标任务完成，请进行下一步处理。";
            String notice = configService.getValue("signFinishNotice");
            if (!StringUtils.isEmpty(notice)) {
                String[] to = notice.split(Global.SEPARATING_SYMBOL);
                mailUtil.sendMail(title, content, to,null);
            }
        } catch (Exception e) {
            logger.info("执行异常");
        } finally {
            scheduledExecutorService.shutdown();
        }
        logger.info("任务执行完成");
    }

    private static final String CRM_ENTERPRISE_WX_TOPIC = "topic_crm_enterprisewx";
    private static final String ENTERPRISE_WX_PUSH = "ENTERPRISE_WX_PUSH";

    public void sendEnterpriseWxMsg(Long mid) {
        MQData mqData = new MQData();
        mqData.setData(mid);
        mqData.setBusiness(ENTERPRISE_WX_PUSH);
        mqProducer.send(CRM_ENTERPRISE_WX_TOPIC, null, mqData);
    }

    /**
     *批量修改配送日期消息发送
     * @param isPlanDeliveryDateList
     */
    public void sendBatchUpdateDeliveryDateMesaageAndWeChat(ArrayList<BatchUpdateDeliveryDateVo> isPlanDeliveryDateList) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("MM/dd");
        DateTimeFormatter wcDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (!CollectionUtils.isEmpty(isPlanDeliveryDateList)) {
            for (BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo : isPlanDeliveryDateList) {
                //短信-订单延期
                String oldDeliveryTime = batchUpdateDeliveryDateVo.getDeliveryTime().format(dtf);
                String newDeliveryTime = batchUpdateDeliveryDateVo.getNewDeliveryTime().format(dtf);
                String managePhone = batchUpdateDeliveryDateVo.getPhone();
                try {
                    SMS sms = new SMS();
                    sms.setPhone(batchUpdateDeliveryDateVo.getSendPhone());
                    sms.setArgs(Arrays.asList(oldDeliveryTime, newDeliveryTime, managePhone == null ? " " : managePhone));
                    sms.setType(SMSType.NOTIFY);
                    sms.setSceneId(13L);
                    smsSenderFactory.getSMSSender().sendSMS(sms);
                } catch (Exception e) {
                    logger.error(batchUpdateDeliveryDateVo.getOrderNo() + "订单延期短信发送失败", e);
                }

                //微信公众号推送-订单延期
                DeliveryPlanChangeWeChatVo deliveryPlanChangeWeChatVo = new DeliveryPlanChangeWeChatVo();
                deliveryPlanChangeWeChatVo
                        .setOpenid(batchUpdateDeliveryDateVo.getOpenid())
                        .setOrderNo(batchUpdateDeliveryDateVo.getOrderNo())
                        .setOldDliveryDates(batchUpdateDeliveryDateVo.getDeliveryTime().format(wcDtf))
                        .setNewDeliveryDate(batchUpdateDeliveryDateVo.getNewDeliveryTime().format(wcDtf));
                try {
                    String msg = DeliveryPlanChangeMsg.batchDeliveryPlanChange(deliveryPlanChangeWeChatVo);
                    templateMsgSender.sendTemplateMsg(msg);
                } catch (Exception e) {
                    logger.error(batchUpdateDeliveryDateVo.getOrderNo() + "订单延期微信公众号发送失败", e);
                }
            }
        }
    }
}
